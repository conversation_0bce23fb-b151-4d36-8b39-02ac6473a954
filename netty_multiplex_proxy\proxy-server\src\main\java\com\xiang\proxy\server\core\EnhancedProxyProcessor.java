package com.xiang.proxy.server.core;

import com.xiang.proxy.server.config.ProxyProcessorConfig;
import com.xiang.proxy.server.router.Router;
import com.xiang.proxy.server.router.RouteResult;
import com.xiang.proxy.server.outbound.OutboundHandler;
import com.xiang.proxy.server.outbound.OutboundConnection;
import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;

import java.util.concurrent.BlockingQueue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 增强的代理处理器 - 参考server DispatchProcessor的设计并改进
 * 集成ActiveConnectionManager实现连接复用和消息队列缓存
 * 支持多路复用协议和各种连接类型
 */
public class EnhancedProxyProcessor extends ProxyProcessor {
    private static final Logger logger = LoggerFactory.getLogger(EnhancedProxyProcessor.class);

    public EnhancedProxyProcessor(Router router) {
        super(router);
        logger.info("创建增强代理处理器（默认配置）");
    }

    public EnhancedProxyProcessor(Router router, ProxyProcessorConfig config) {
        super(router, config);
        logger.info("创建增强代理处理器: {}", config);
    }

    /**
     * 重写队列处理逻辑，集成连接复用机制
     */
    @Override
    protected void processQueue(int queueIndex) {
        logger.debug("增强队列 {} 工作线程启动", queueIndex);
        BlockingQueue<QueuedRequest> queue = getRequestQueue(queueIndex);
        ActiveConnectionManager connectionManager = getConnectionManager(queueIndex);
        
        while (getRunningState().get()) {
            try {
                QueuedRequest queuedRequest = queue.poll(1, TimeUnit.SECONDS);
                if (queuedRequest != null) {
                    processQueuedRequestWithConnectionReuse(queuedRequest, queueIndex, connectionManager);
                } else {
                    // 空闲时处理连接维护
                    performConnectionMaintenance(connectionManager);
                }
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.info("增强队列 {} 工作线程被中断", queueIndex);
                break;
            } catch (Exception e) {
                logger.error("增强队列 {} 处理请求时发生异常", queueIndex, e);
            }
        }
        
        logger.debug("增强队列 {} 工作线程结束", queueIndex);
    }

    /**
     * 使用连接复用处理请求
     */
    private void processQueuedRequestWithConnectionReuse(QueuedRequest queuedRequest, int queueIndex, 
                                                       ActiveConnectionManager connectionManager) {
        ProxyRequest request = queuedRequest.getRequest();
        CompletableFuture<ProxyResponse> future = queuedRequest.getFuture();
        
        try {
            // 路由请求
            RouteResult routeResult = getRouter().route(request);
            if (!routeResult.isSuccess()) {
                future.complete(ProxyResponse.failure(request.getRequestId(), routeResult.getReason()));
                return;
            }

            // 获取或创建活跃连接
            ActiveConnectionManager.ActiveConnection activeConnection = connectionManager.getOrCreateConnection(
                request.getTargetHost(), 
                request.getTargetPort(), 
                request.getClientId(), 
                request.getProtocol()
            );

            // 检查连接状态
            ChannelFuture channelFuture = activeConnection.getChannelFuture();
            if (channelFuture != null && channelFuture.isDone() && channelFuture.isSuccess()) {
                Channel channel = channelFuture.channel();
                if (channel.isActive()) {
                    // 连接可用，直接发送数据
                    handleActiveConnection(request, future, activeConnection, channel);
                } else {
                    // 连接已断开，重新建立
                    establishNewConnection(request, future, activeConnection, routeResult);
                }
            } else {
                // 连接未建立或正在建立中
                if (channelFuture == null) {
                    // 需要建立新连接
                    establishNewConnection(request, future, activeConnection, routeResult);
                } else {
                    // 连接正在建立中，将消息加入队列
                    queueMessageForPendingConnection(request, future, activeConnection);
                }
            }

        } catch (Exception e) {
            logger.error("处理连接复用请求时发生异常: {}", request.getRequestId(), e);
            future.complete(ProxyResponse.error(request.getRequestId(), e.getMessage()));
        }
    }

    /**
     * 处理活跃连接
     */
    private void handleActiveConnection(ProxyRequest request, CompletableFuture<ProxyResponse> future,
                                      ActiveConnectionManager.ActiveConnection activeConnection, Channel channel) {
        try {
            // 刷新队列中的消息
            activeConnection.flushQueuedMessages(channel);
            
            // 发送当前请求的数据
            if (request.getData() != null) {
                ByteBuf data = request.getData();
                boolean sent = activeConnection.sendMessage(data.retain());
                if (sent) {
                    future.complete(ProxyResponse.success(request.getRequestId(), "数据已发送"));
                } else {
                    future.complete(ProxyResponse.failure(request.getRequestId(), "发送失败，队列已满"));
                }
            } else {
                future.complete(ProxyResponse.success(request.getRequestId(), "连接已建立"));
            }
            
        } catch (Exception e) {
            logger.error("处理活跃连接时发生异常: {}", request.getRequestId(), e);
            future.complete(ProxyResponse.error(request.getRequestId(), e.getMessage()));
        }
    }

    /**
     * 建立新连接
     */
    private void establishNewConnection(ProxyRequest request, CompletableFuture<ProxyResponse> future,
                                      ActiveConnectionManager.ActiveConnection activeConnection, RouteResult routeResult) {
        try {
            // 获取出站处理器
            OutboundHandler outboundHandler = getOutboundHandler(routeResult.getOutboundId());
            if (outboundHandler == null) {
                future.complete(ProxyResponse.failure(request.getRequestId(), 
                    "未找到出站处理器: " + routeResult.getOutboundId()));
                return;
            }

            // 建立连接
            CompletableFuture<OutboundConnection> connectFuture = outboundHandler.connect(request);

            // 处理连接结果
            connectFuture.whenComplete((outboundConnection, throwable) -> {
                if (throwable != null) {
                    logger.warn("建立连接失败: {}:{}", request.getTargetHost(), request.getTargetPort(), throwable);
                    future.complete(ProxyResponse.failure(request.getRequestId(), "连接失败: " + throwable.getMessage()));
                } else {
                    logger.debug("连接建立成功: {}:{}", request.getTargetHost(), request.getTargetPort());

                    // 从OutboundConnection获取Channel
                    Channel channel = outboundConnection.getBackendChannel();
                    if (channel != null) {
                        // 创建简单的ChannelFuture适配器
                        ChannelFuture channelFuture = new SimpleChannelFuture(channel);
                        activeConnection.setChannelFuture(channelFuture);

                        // 连接建立成功后，处理队列中的消息
                        activeConnection.flushQueuedMessages(channel);

                        // 发送当前请求的数据
                        if (request.getData() != null) {
                            ByteBuf data = request.getData();
                            boolean sent = activeConnection.sendMessage(data.retain());
                            if (sent) {
                                future.complete(ProxyResponse.success(request.getRequestId(), "连接已建立并发送数据"));
                            } else {
                                future.complete(ProxyResponse.failure(request.getRequestId(), "连接已建立但发送失败"));
                            }
                        } else {
                            future.complete(ProxyResponse.success(request.getRequestId(), "连接已建立"));
                        }
                    } else {
                        future.complete(ProxyResponse.failure(request.getRequestId(), "连接建立成功但Channel为空"));
                    }
                }
            });

        } catch (Exception e) {
            logger.error("建立新连接时发生异常: {}", request.getRequestId(), e);
            future.complete(ProxyResponse.error(request.getRequestId(), e.getMessage()));
        }
    }

    /**
     * 为待建立的连接排队消息
     */
    private void queueMessageForPendingConnection(ProxyRequest request, CompletableFuture<ProxyResponse> future,
                                                ActiveConnectionManager.ActiveConnection activeConnection) {
        try {
            if (request.getData() != null) {
                ByteBuf data = request.getData();
                boolean queued = activeConnection.sendMessage(data.retain());
                if (queued) {
                    future.complete(ProxyResponse.success(request.getRequestId(), "消息已排队，等待连接建立"));
                } else {
                    future.complete(ProxyResponse.failure(request.getRequestId(), "消息队列已满"));
                }
            } else {
                future.complete(ProxyResponse.success(request.getRequestId(), "等待连接建立"));
            }
        } catch (Exception e) {
            logger.error("排队消息时发生异常: {}", request.getRequestId(), e);
            future.complete(ProxyResponse.error(request.getRequestId(), e.getMessage()));
        }
    }

    /**
     * 执行连接维护 - 在空闲时调用
     */
    private void performConnectionMaintenance(ActiveConnectionManager connectionManager) {
        try {
            // 这里可以添加连接健康检查、预热等逻辑
            // 目前只是简单的日志记录
            if (logger.isTraceEnabled()) {
                ActiveConnectionManager.ConnectionStats stats = connectionManager.getStats();
                logger.trace("连接维护检查: {}", stats);
            }
        } catch (Exception e) {
            logger.warn("执行连接维护时发生异常", e);
        }
    }

    /**
     * 简单的ChannelFuture实现 - 用于已完成的连接
     */
    private static class SimpleChannelFuture implements ChannelFuture {
        private final Channel channel;

        public SimpleChannelFuture(Channel channel) {
            this.channel = channel;
        }

        @Override
        public Channel channel() {
            return channel;
        }

        @Override
        public boolean isDone() {
            return true;
        }

        @Override
        public boolean isSuccess() {
            return channel != null && channel.isActive();
        }

        @Override
        public Throwable cause() {
            return null;
        }

        @Override
        public boolean isVoid() {
            return false;
        }

        // 简化的实现 - 对于已完成的连接，大部分方法都是空操作
        @Override
        public ChannelFuture addListener(io.netty.util.concurrent.GenericFutureListener<? extends io.netty.util.concurrent.Future<? super Void>> listener) {
            return this;
        }

        @Override
        @SafeVarargs
        public final ChannelFuture addListeners(io.netty.util.concurrent.GenericFutureListener<? extends io.netty.util.concurrent.Future<? super Void>>... listeners) {
            return this;
        }

        @Override
        public ChannelFuture removeListener(io.netty.util.concurrent.GenericFutureListener<? extends io.netty.util.concurrent.Future<? super Void>> listener) {
            return this;
        }

        @Override
        @SafeVarargs
        public final ChannelFuture removeListeners(io.netty.util.concurrent.GenericFutureListener<? extends io.netty.util.concurrent.Future<? super Void>>... listeners) {
            return this;
        }

        @Override
        public ChannelFuture sync() throws InterruptedException {
            return this;
        }

        @Override
        public ChannelFuture syncUninterruptibly() {
            return this;
        }

        @Override
        public ChannelFuture await() throws InterruptedException {
            return this;
        }

        @Override
        public ChannelFuture awaitUninterruptibly() {
            return this;
        }

        @Override
        public boolean await(long timeout, TimeUnit unit) throws InterruptedException {
            return true;
        }

        @Override
        public boolean await(long timeoutMillis) throws InterruptedException {
            return true;
        }

        @Override
        public boolean awaitUninterruptibly(long timeout, TimeUnit unit) {
            return true;
        }

        @Override
        public boolean awaitUninterruptibly(long timeoutMillis) {
            return true;
        }

        @Override
        public Void getNow() {
            return null;
        }

        @Override
        public boolean cancel(boolean mayInterruptIfRunning) {
            return false;
        }

        @Override
        public boolean isCancelled() {
            return false;
        }

        @Override
        public Void get() throws InterruptedException, java.util.concurrent.ExecutionException {
            return null;
        }

        @Override
        public Void get(long timeout, TimeUnit unit) throws InterruptedException, java.util.concurrent.ExecutionException, java.util.concurrent.TimeoutException {
            return null;
        }

        @Override
        public boolean isCancellable() {
            return false;
        }
    }
}
