package com.xiang.proxy.server.inbound.impl.multiplex;

import com.xiang.proxy.server.auth.AuthManager;
import com.xiang.proxy.server.blacklist.HostBlacklist;
import com.xiang.proxy.server.core.ProxyProcessor;
import com.xiang.proxy.server.core.ProxyRequest;
import com.xiang.proxy.server.filter.FilterResult;
import com.xiang.proxy.server.filter.GeoLocationFilter;
import com.xiang.proxy.server.metrics.AdvancedMetrics;
import com.xiang.proxy.server.metrics.PerformanceMetrics;
import com.xiang.proxy.server.outbound.OutboundConnection;
import com.xiang.proxy.server.outbound.OutboundHandler;
import com.xiang.proxy.server.protocol.MultiplexProtocol;
import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.Queue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 多路复用会话 V2
 * 集成了完整的认证、连接池、黑名单、地理位置过滤、性能监控功能
 */
public class MultiplexSession {
    private static final Logger logger = LoggerFactory.getLogger(MultiplexSession.class);

    // 每个客户端的最大会话数限制
    private static final int MAX_SESSIONS_PER_CLIENT = 200;
    // 已关闭会话的短期缓存TTL，用于抑制重复日志（毫秒）
    private static final long CLOSED_SESSION_TTL_MS = 10_000L;

    private final Channel clientChannel;
    private final ProxyProcessor proxyProcessor;
    private final long clientConnectionId;
    private final Map<Integer, String> sessionConnections = new ConcurrentHashMap<>();
    private final Map<Integer, String> sessionHostKeys = new ConcurrentHashMap<>(); // 会话ID到主机键值的映射
    // 已关闭/出错会话的时间戳，避免客户端重复发送数据导致日志泛滥
    private final ConcurrentHashMap<Integer, Long> terminatedSessions = new ConcurrentHashMap<>();
    private final AtomicInteger sessionIdGenerator = new AtomicInteger(1); // 从1开始生成sessionId
    private final Queue<Integer> reusableSessionIds = new ConcurrentLinkedQueue<>(); // 可重用的会话ID队列
    private ByteBuf buffer;

    // 添加性能指标收集实例
    private final AdvancedMetrics advancedMetrics = AdvancedMetrics.getInstance();

    public MultiplexSession(Channel clientChannel, ProxyProcessor proxyProcessor, long clientConnectionId) {
        this.clientChannel = clientChannel;
        this.proxyProcessor = proxyProcessor;
        this.clientConnectionId = clientConnectionId;
    }

    /**
     * 处理接收到的数据
     */
    public void processData(ByteBuf data) {
        try {
            // 优化：延迟创建缓冲区，减少内存分配
            if (buffer == null) {
                // 如果数据足够大，直接处理而不创建缓冲区
                if (data.readableBytes() >= MultiplexProtocol.HEADER_LENGTH) {
                    if (tryProcessDirectly(data)) {
                        return; // 直接处理成功，无需缓冲
                    }
                }
                // 需要缓冲，创建适当大小的缓冲区
                buffer = clientChannel.alloc().buffer(Math.max(data.readableBytes() * 2, 1024));
            }

            buffer.writeBytes(data);

            // 尝试解析数据包
            while (buffer.readableBytes() >= MultiplexProtocol.HEADER_LENGTH) {
                int readerIndex = buffer.readerIndex();
                MultiplexProtocol.Packet packet = MultiplexProtocol.Packet.decode(buffer);

                if (packet == null) {
                    // 数据不完整，等待更多数据
                    buffer.readerIndex(readerIndex);
                    break;
                }

                // 处理数据包
                handlePacket(packet);
            }

            // 优化缓冲区管理
            optimizeBuffer();

        } finally {
            data.release();
        }
    }

    /**
     * 尝试直接处理数据而不使用缓冲区
     */
    private boolean tryProcessDirectly(ByteBuf data) {
        if (data.readableBytes() < MultiplexProtocol.HEADER_LENGTH) {
            return false;
        }

        int readerIndex = data.readerIndex();
        MultiplexProtocol.Packet packet = MultiplexProtocol.Packet.decode(data);

        if (packet == null) {
            // 数据不完整，恢复读取位置
            data.readerIndex(readerIndex);
            return false;
        }

        // 处理数据包
        handlePacket(packet);

        // 如果还有剩余数据，返回false以便使用缓冲区处理
        return data.readableBytes() == 0;
    }

    /**
     * 处理数据包
     */
    private void handlePacket(MultiplexProtocol.Packet packet) {
        logger.debug("收到多路复用数据包: {}", packet);

        try {
            switch (packet.getType()) {
                case MultiplexProtocol.TYPE_AUTH_REQUEST:
                    handleAuthRequest(packet);
                    break;
                case MultiplexProtocol.TYPE_CONNECT_REQUEST_V2:
                    handleConnectRequest(packet, ProxyRequest.Protocol.TCP); // 默认TCP
                    break;
                case MultiplexProtocol.TYPE_TCP_CONNECT_REQUEST:
                    handleConnectRequest(packet, ProxyRequest.Protocol.TCP);
                    break;
                case MultiplexProtocol.TYPE_UDP_CONNECT_REQUEST:
                    handleConnectRequest(packet, ProxyRequest.Protocol.UDP);
                    break;
                case MultiplexProtocol.TYPE_DATA:
                    handleDataPacket(packet);
                    break;
                case MultiplexProtocol.TYPE_CLOSE:
                    handleClosePacket(packet);
                    break;
                case MultiplexProtocol.TYPE_HEARTBEAT:
                    handleHeartbeat(packet);
                    break;
                default:
                    logger.warn("未知数据包类型: {}", packet.getType());
            }
        } catch (Exception e) {
            logger.error("处理数据包时发生异常: {}", packet, e);
        }
    }

    /**
     * 处理认证请求
     */
    private void handleAuthRequest(MultiplexProtocol.Packet packet) {
        try {
            // 解析认证数据
            String[] credentials = MultiplexProtocol.parseAuthRequest(packet);
            String username = credentials[0];
            String password = credentials[1];

            logger.debug("连接 {} 收到认证请求，用户: {}", clientConnectionId, username);

            // 执行认证
            boolean success = AuthManager.getInstance().authenticate(clientChannel, username, password);

            // 发送认证响应
            byte status = success ? MultiplexProtocol.STATUS_SUCCESS : MultiplexProtocol.STATUS_AUTH_FAILED;
            MultiplexProtocol.Packet response = MultiplexProtocol.createAuthResponsePacket(status);
            ByteBuf responseBuffer = response.encode();
            clientChannel.writeAndFlush(responseBuffer);

            if (success) {
                logger.info("连接 {} 认证成功，用户: {}", clientConnectionId, username);
            } else {
                logger.warn("连接 {} 认证失败，用户: {}，即将关闭连接", clientConnectionId, username);
                // 认证失败后延迟关闭连接，给客户端时间接收响应
                clientChannel.eventLoop().schedule(() -> {
                    clientChannel.close();
                }, 1, java.util.concurrent.TimeUnit.SECONDS);
            }

        } catch (Exception e) {
            logger.error("处理认证请求时发生异常 (连接ID: {}): {}", clientConnectionId, e.getMessage(), e);

            // 发送认证失败响应
            MultiplexProtocol.Packet response = MultiplexProtocol
                    .createAuthResponsePacket(MultiplexProtocol.STATUS_AUTH_FAILED);
            ByteBuf responseBuffer = response.encode();
            clientChannel.writeAndFlush(responseBuffer);

            // 关闭连接
            clientChannel.close();
        }
    }

    /**
     * 处理连接请求
     */
    private void handleConnectRequest(MultiplexProtocol.Packet packet, String protocol) {
        try {
            // 检查认证状态
            if (AuthManager.getInstance().requiresAuth(clientChannel)) {
                logger.warn("连接 {} 未认证，拒绝{}连接请求", clientConnectionId, protocol);
                sendConnectResponse(0, MultiplexProtocol.STATUS_AUTH_REQUIRED);
                return;
            }

            // 检查认证超时
            if (AuthManager.getInstance().isAuthTimeout(clientChannel)) {
                logger.warn("连接 {} 认证超时，关闭连接", clientConnectionId);
                clientChannel.close();
                return;
            }

            String[] hostPort = MultiplexProtocol.parseConnectRequest(packet);
            String host = hostPort[0];
            int port = Integer.parseInt(hostPort[1]);

            // 安全地分配新的sessionId，避免冲突
            int sessionId = allocateSessionId();
            if (sessionId == -1) {
                logger.error("无法分配会话ID，会话数量已达上限 (连接ID: {})", clientConnectionId);
                sendConnectResponse(0, MultiplexProtocol.STATUS_FAILED);
                return;
            }

            // 新会话建立前，移除该ID的“已终止”标记，避免误判
            terminatedSessions.remove(sessionId);

            // 格式化IPv6地址用于日志显示
            String displayAddress = formatAddressForDisplay(host, port);
            logger.info("客户端连接 {} 分配会话 {} 请求{}连接: {}",
                    clientConnectionId, sessionId, protocol, displayAddress);

            // 检查主机是否在黑名单中
            if (HostBlacklist.getInstance().isBlacklisted(host)) {
                PerformanceMetrics.getInstance().incrementBlacklistHits();
                if (logger.isDebugEnabled()) {
                    logger.debug("主机 {} 在黑名单中，拒绝{}连接请求 (连接ID: {}, 会话: {})",
                            host, protocol, clientConnectionId, sessionId);
                }
                sendConnectResponse(sessionId, MultiplexProtocol.STATUS_HOST_UNREACHABLE);
                return;
            }

            // 地理位置过滤检查
            FilterResult filterResult = GeoLocationFilter.getInstance().checkAccess(host, port);
            if (filterResult.isBlocked()) {
                PerformanceMetrics.getInstance().incrementGeoLocationBlocks();
                logger.warn("主机 {} 被地理位置过滤器阻止，原因: {} (连接ID: {}, 会话: {}, 类型: {})",
                        host, filterResult.getReason(), clientConnectionId, sessionId, protocol);

                byte statusCode = MultiplexProtocol.STATUS_HOST_UNREACHABLE;
                switch (filterResult.getBlockReason()) {
                    case MALICIOUS_DOMAIN:
                    case MALICIOUS_KEYWORDS:
                        statusCode = MultiplexProtocol.STATUS_FORBIDDEN;
                        break;
                    case OVERSEAS_SUSPICIOUS:
                    case GEO_LOCATION_RESTRICTED:
                        statusCode = MultiplexProtocol.STATUS_HOST_UNREACHABLE;
                        break;
                    default:
                        statusCode = MultiplexProtocol.STATUS_FAILED;
                        break;
                }
                sendConnectResponse(sessionId, statusCode);
                return;
            }

            // 检查会话数量限制
            if (sessionConnections.size() >= MAX_SESSIONS_PER_CLIENT) {
                logger.warn("客户端连接 {} 会话数量已达上限 {}，拒绝新{}连接请求",
                        clientConnectionId, MAX_SESSIONS_PER_CLIENT, protocol);
                sendConnectResponse(sessionId, MultiplexProtocol.STATUS_FAILED);
                return;
            }

            // 创建代理请求，通过ProxyProcessor处理
            ProxyRequest request = ProxyRequest.builder()
                    .protocol(protocol)
                    .target(host, port)
                    .clientChannel(clientChannel)
                    .sessionId(sessionId)
                    .clientId(clientChannel.id().asShortText())
                    .attribute(ProxyRequest.Attributes.ORIGINAL_PROTOCOL, ProxyRequest.Protocol.MULTIPLEX)
                    .build();

            // 通过ProxyProcessor处理请求，由outbound处理连接
            long startTime = System.currentTimeMillis();
            proxyProcessor.processRequest(request)
                    .whenComplete((response, throwable) -> {
                        long elapsedTime = System.currentTimeMillis() - startTime;

                        if (throwable != null) {
                            logger.error("处理连接请求失败: sessionId={}, target={}:{}, 耗时: {}ms",
                                    sessionId, host, port, elapsedTime, throwable);

                            // Record connection quality metrics - failure
                            advancedMetrics.recordConnectionQuality(host, false, elapsedTime);
                            advancedMetrics.recordError("backend_connection_failed");
                            HostBlacklist.getInstance().recordFailure(host);

                            handleConnectionFailure(sessionId, host, port, protocol, throwable);
                        } else if (response.isSuccess() && response.hasConnection()) {
                            logger.debug("连接请求处理成功: sessionId={}, target={}:{}, 耗时: {}ms",
                                    sessionId, host, port, elapsedTime);

                            // Record connection quality metrics - success
                            advancedMetrics.recordConnectionQuality(host, true, elapsedTime);
                            HostBlacklist.getInstance().recordSuccess(host);

                            String connectionId = response.getConnection().getConnectionId();
                            String hostKey = host + ":" + port + ":" + protocol+ ":" + request.getClientId();

                            sessionConnections.put(sessionId, connectionId);
                            sessionHostKeys.put(sessionId, hostKey);
                            sendConnectResponse(sessionId, MultiplexProtocol.STATUS_SUCCESS);

                            PerformanceMetrics.getInstance().incrementTotalSessions();
                            PerformanceMetrics.getInstance().incrementActiveSessions();

                            // 设置后端数据处理器
                            setupBackendHandler(response.getConnection(), sessionId);
                        } else {
                            logger.warn("连接请求处理失败: sessionId={}, target={}:{}, reason={}, 耗时: {}ms",
                                    sessionId, host, port, response.getMessage(), elapsedTime);

                            // Record connection quality metrics - failure
                            advancedMetrics.recordConnectionQuality(host, false, elapsedTime);
                            HostBlacklist.getInstance().recordFailure(host);

                            sendConnectResponse(sessionId, MultiplexProtocol.STATUS_FAILED);
                        }
                    });

        } catch (Exception e) {
            logger.error("解析连接请求失败: {}", packet, e);
            sendConnectResponse(packet.getSessionId(), MultiplexProtocol.STATUS_FAILED);
        }
    }

    /**
     * 处理数据包
     */
    private void handleDataPacket(MultiplexProtocol.Packet packet) {
        int sessionId = packet.getSessionId();
        String connectionId = sessionConnections.get(sessionId);

        if (connectionId == null) {
            // 若该会话近期已关闭，静默丢弃以抑制日志泛滥
            Long closedAt = terminatedSessions.get(sessionId);
            long now = System.currentTimeMillis();
            if (closedAt != null && (now - closedAt) < CLOSED_SESSION_TTL_MS) {
                logger.debug("收到已关闭会话的数据，忽略: sessionId={}", sessionId);
                return;
            }

            // 首次或超过TTL后再次收到，告警一次并发送关闭通知
            logger.warn("未找到会话对应的连接: sessionId={}", sessionId);
            terminatedSessions.put(sessionId, now);
            sendCloseNotification(sessionId, "会话不存在");
            return;
        }

        // 获取连接并检查状态
        OutboundConnection connection = proxyProcessor.getActiveConnection(connectionId);
        if (connection == null) {
            logger.debug("连接不存在: sessionId={}, connectionId={}", sessionId, connectionId);
            handleSessionError(sessionId, "连接不存在");
            return;
        }

        if (!connection.isActive()) {
            logger.debug("连接已关闭: sessionId={}, connectionId={}", sessionId, connectionId);
            handleSessionError(sessionId, "连接已关闭");
            return;
        }

        // 获取对应的outbound处理器
        String outboundId = connection.getAttribute(OutboundConnection.Attributes.OUTBOUND_ID);
        OutboundHandler outboundHandler = proxyProcessor.getOutboundHandler(outboundId);

        if (outboundHandler == null) {
            logger.error("未找到outbound处理器: outboundId={}", outboundId);
            handleSessionError(sessionId, "outbound处理器不存在");
            return;
        }

        // 获取数据并通过outbound处理器发送
        ByteBuf dataBuf = packet.getDataBuf();
        if (dataBuf != null && dataBuf.isReadable()) {
            // 直接通过outbound处理器发送数据，不检查后端连接状态
            // 让outbound处理器负责连接管理和数据发送
            outboundHandler.sendData(connection, dataBuf)
                    .whenComplete((result, throwable) -> {
                        if (throwable != null) {
                            // 使用智能错误分类，避免Connection reset by peer的ERROR级别日志
                            if (isConnectionResetError(throwable)) {
                                logger.debug("会话连接被重置: sessionId={}, connectionId={} (连接ID: {})",
                                        sessionId, connectionId, clientConnectionId);
                            } else if (isClosedChannelError(throwable)) {
                                logger.debug("会话通道已关闭: sessionId={}, connectionId={} (连接ID: {})",
                                        sessionId, connectionId, clientConnectionId);
                            } else {
                                logger.warn("发送数据到outbound失败: sessionId={}, connectionId={} (连接ID: {}), error={}",
                                        sessionId, connectionId, clientConnectionId, throwable.getMessage());
                            }
                            // 发送失败，关闭会话
                            handleSessionError(sessionId, "数据发送失败: " + throwable.getMessage());
                        } else {
                            logger.debug("数据转发成功: sessionId={}, connectionId={}, bytes={}",
                                    sessionId, connectionId, dataBuf.readableBytes());
                        }
                    });
        } else {
            logger.debug("收到空数据包: sessionId={}", sessionId);
        }
    }

    /**
     * 处理关闭包
     */
    private void handleClosePacket(MultiplexProtocol.Packet packet) {
        int sessionId = packet.getSessionId();
        String connectionId = sessionConnections.remove(sessionId);
        sessionHostKeys.remove(sessionId);

        // 回收会话ID以供重用
        if (sessionId > 0 && sessionId <= 10000) {
            reusableSessionIds.offer(sessionId);
            logger.debug("回收会话ID {} 以供重用 (连接ID: {})", sessionId, clientConnectionId);
        }

        // 标记该会话已终止，防止客户端后续重复数据触发日志洪泛
        terminatedSessions.put(sessionId, System.currentTimeMillis());

        if (connectionId != null) {
            logger.debug("关闭会话 {} (连接ID: {})", sessionId, clientConnectionId);

            // 通过ProxyProcessor关闭连接，让outbound处理器负责连接管理
            proxyProcessor.closeConnection(connectionId)
                    .whenComplete((result, throwable) -> {
                        if (throwable != null) {
                            logger.warn("关闭连接失败: sessionId={}, connectionId={} (连接ID: {})",
                                    sessionId, connectionId, clientConnectionId, throwable);
                        } else {
                            logger.debug("连接关闭成功: sessionId={}, connectionId={} (连接ID: {})",
                                    sessionId, connectionId, clientConnectionId);
                        }
                        PerformanceMetrics.getInstance().decrementActiveSessions();
                    });
        } else {
            logger.debug("尝试关闭不存在的会话 {} (连接ID: {})", sessionId, clientConnectionId);
        }
    }

    /**
     * 处理心跳包
     * 客户端发送心跳包，服务端简单回复心跳响应
     */
    private void handleHeartbeat(MultiplexProtocol.Packet packet) {
        try {
            logger.debug("收到客户端心跳包，回复心跳响应");

            // 创建心跳响应包
            MultiplexProtocol.Packet response = MultiplexProtocol.createHeartbeatPacket();
            ByteBuf responseBuffer = response.encode();

            // 发送心跳响应
            clientChannel.writeAndFlush(responseBuffer).addListener(future -> {
                if (future.isSuccess()) {
                    logger.debug("心跳响应发送成功");
                } else {
                    logger.warn("心跳响应发送失败", future.cause());
                }
            });

        } catch (Exception e) {
            logger.error("处理心跳包时发生异常", e);
        }
    }

    /**
     * 发送连接响应
     */
    private void sendConnectResponse(int sessionId, byte status) {
        try {
            MultiplexProtocol.Packet response = MultiplexProtocol.createConnectResponseV2(sessionId, status);
            ByteBuf responseBuffer = response.encode();
            clientChannel.writeAndFlush(responseBuffer).addListener(future -> {
                if (!future.isSuccess()) {
                    logger.error("发送连接响应失败: sessionId={}, status={}", sessionId, status, future.cause());
                } else {
                    logger.debug("发送连接响应成功: sessionId={}, status={}", sessionId, status);
                }
            });
        } catch (Exception e) {
            logger.error("创建连接响应失败: sessionId={}, status={}", sessionId, status, e);
        }
    }

    /**
     * 设置后端数据处理器
     */
    private void setupBackendHandler(com.xiang.proxy.server.outbound.OutboundConnection connection, int sessionId) {
        Channel backendChannel = connection.getBackendChannel();
        if (backendChannel == null) {
            logger.warn("后端连接通道为空: sessionId={}", sessionId);
            handleSessionError(sessionId, "后端连接通道为空");
            return;
        }

        // 检查连接是否仍然活跃
        if (!backendChannel.isActive()) {
            logger.warn("后端连接已断开: sessionId={}", sessionId);
            handleSessionError(sessionId, "后端连接已断开");
            return;
        }

        try {
            // 检查pipeline中是否已经存在处理器，移除
            if (backendChannel.pipeline().get("multiplex-backend-handler") != null) {
                backendChannel.pipeline().remove("multiplex-backend-handler");
                logger.debug("移除旧的后端处理器");
            }

            // 添加后端数据处理器到pipeline
            backendChannel.pipeline().addLast("multiplex-backend-handler",
                    new MultiplexBackendDataHandler(clientChannel, sessionId, this::handleSessionError));

            logger.debug("后端数据处理器设置完成: sessionId={}, connectionId={}",
                    sessionId, connection.getConnectionId());

        } catch (Exception e) {
            logger.error("设置后端数据处理器失败: sessionId={}", sessionId, e);
            handleSessionError(sessionId, "设置后端处理器失败: " + e.getMessage());
        }
    }

    /**
     * 处理会话错误
     */
    private void handleSessionError(int sessionId, String reason) {
        // 检查会话是否已经被处理过，避免重复处理
        String connectionId = sessionConnections.get(sessionId);
        if (connectionId == null) {
            logger.debug("会话已被处理，跳过重复错误处理: sessionId={}, reason={} (连接ID: {})",
                    sessionId, reason, clientConnectionId);
            return;
        }

        // 根据错误原因调整日志级别，减少Connection reset错误的噪音
        if (isConnectionResetError(reason)) {
            logger.debug("会话连接被重置: sessionId={}, reason={} (连接ID: {})",
                    sessionId, reason, clientConnectionId);
        } else if (isCommonNetworkError(reason)) {
            logger.debug("会话网络错误: sessionId={}, reason={} (连接ID: {})",
                    sessionId, reason, clientConnectionId);
        } else {
            logger.warn("会话发生错误: sessionId={}, reason={} (连接ID: {})",
                    sessionId, reason, clientConnectionId);
        }

        // 移除会话连接映射
        sessionConnections.remove(sessionId);
        sessionHostKeys.remove(sessionId);

        // 回收会话ID以供重用
        if (sessionId > 0 && sessionId <= 10000) {
            reusableSessionIds.offer(sessionId);
            logger.debug("回收会话ID {} 以供重用 (连接ID: {})", sessionId, clientConnectionId);
        }

        // 标记该会话已终止，防止客户端后续重复数据触发日志洪泛
        terminatedSessions.put(sessionId, System.currentTimeMillis());

        // 通过ProxyProcessor关闭连接，让outbound处理器负责连接管理
        if (connectionId != null) {
            proxyProcessor.closeConnection(connectionId);
            PerformanceMetrics.getInstance().decrementActiveSessions();
        }

        // 发送关闭通知给客户端
        sendCloseNotification(sessionId, reason);
    }

    /**
     * 判断是否为连接重置错误
     */
    private boolean isConnectionResetError(String reason) {
        return reason != null && (reason.contains("Connection reset by peer") ||
                reason.contains("Connection reset") ||
                reason.contains("连接被重置") ||
                reason.contains("远程主机强迫关闭了一个现有的连接"));
    }

    /**
     * 判断是否为常见网络错误
     */
    private boolean isCommonNetworkError(String reason) {
        return reason != null && (reason.contains("timeout") ||
                reason.contains("超时") ||
                reason.contains("Connection refused") ||
                reason.contains("连接被拒绝") ||
                reason.contains("Network is unreachable") ||
                reason.contains("网络不可达"));
    }

    /**
     * 发送关闭通知给客户端
     */
    private void sendCloseNotification(int sessionId, String reason) {
        try {
            MultiplexProtocol.Packet closePacket = MultiplexProtocol.createClosePacket(sessionId);
            ByteBuf closeBuffer = closePacket.encode();
            clientChannel.writeAndFlush(closeBuffer);

            logger.debug("发送关闭通知: sessionId={}, reason={}", sessionId, reason);
        } catch (Exception e) {
            logger.warn("发送关闭通知失败: sessionId={}", sessionId, e);
        }
    }

    /**
     * 处理连接失败
     */
    private void handleConnectionFailure(int sessionId, String host, int port, String protocol, Throwable cause) {
        byte statusCode = MultiplexProtocol.STATUS_FAILED;
        if (cause instanceof java.net.ConnectException) {
            statusCode = MultiplexProtocol.STATUS_HOST_UNREACHABLE;
            logger.warn("目标主机不可达: {}:{}", host, port);
        } else if (cause instanceof java.net.UnknownHostException) {
            statusCode = MultiplexProtocol.STATUS_HOST_UNREACHABLE;
            logger.warn("无法解析主机名: {}", host);
        } else if (cause instanceof java.util.concurrent.TimeoutException ||
                (cause.getMessage() != null && cause.getMessage().contains("timeout"))) {
            statusCode = MultiplexProtocol.STATUS_HOST_UNREACHABLE;
            logger.warn("连接超时: {}:{}", host, port);
        }

        sendConnectResponse(sessionId, statusCode);
    }

    /**
     * 格式化地址用于显示
     */
    private String formatAddressForDisplay(String host, int port) {
        if (host.contains(":") && !host.startsWith("[")) {
            return "[" + host + "]:" + port;
        }
        return host + ":" + port;
    }

    /**
     * 优化缓冲区管理
     */
    private void optimizeBuffer() {
        if (buffer == null)
            return;

        int readableBytes = buffer.readableBytes();
        if (readableBytes == 0) {
            // 缓冲区为空，清理
            buffer.clear();
        } else if (readableBytes < buffer.capacity() / 4 && buffer.capacity() > 1024) {
            // 缓冲区使用率低且较大，压缩以节省内存
            buffer.discardReadBytes();
            if (buffer.capacity() > readableBytes * 4) {
                // 创建更小的缓冲区
                ByteBuf newBuffer = buffer.alloc().buffer(Math.max(readableBytes * 2, 256));
                newBuffer.writeBytes(buffer);
                buffer.release();
                buffer = newBuffer;
            }
        } else {
            // 正常压缩
            buffer.discardReadBytes();
        }
    }

    /**
     * 根据会话ID获取连接
     */
    private OutboundConnection getConnectionBySessionId(int sessionId) {
        String connectionId = sessionConnections.get(sessionId);
        if (connectionId == null) {
            return null;
        }
        return proxyProcessor.getActiveConnection(connectionId);
    }

    /**
     * 检查会话是否存在
     */
    private boolean isSessionActive(int sessionId) {
        String connectionId = sessionConnections.get(sessionId);
        if (connectionId == null) {
            return false;
        }

        OutboundConnection connection = proxyProcessor.getActiveConnection(connectionId);
        return connection != null && connection.isActive();
    }

    /**
     * 安全地分配会话ID，支持ID重用
     */
    private int allocateSessionId() {
        // 首先尝试重用已释放的会话ID
        Integer reusedId = reusableSessionIds.poll();
        if (reusedId != null && !sessionConnections.containsKey(reusedId)) {
            logger.debug("重用会话ID: {}", reusedId);
            return reusedId;
        }

        // 如果没有可重用的ID，生成新的ID
        int maxAttempts = 100;
        int attempts = 0;

        while (attempts < maxAttempts) {
            int sessionId = sessionIdGenerator.getAndIncrement();

            // 避免sessionId溢出，重置为1
            if (sessionId <= 0 || sessionId > 10000) {
                sessionIdGenerator.set(1);
                sessionId = sessionIdGenerator.getAndIncrement();
            }

            // 检查是否已存在
            if (!sessionConnections.containsKey(sessionId)) {
                return sessionId;
            }

            attempts++;
        }

        // 如果无法分配，返回-1表示失败
        logger.error("无法分配会话ID，尝试了{}次", maxAttempts);
        return -1;
    }

    /**
     * 获取会话统计信息
     */
    public int getActiveSessionCount() {
        return sessionConnections.size();
    }

    /**
     * 清理会话
     */
    public void cleanup() {
        logger.debug("开始清理多路复用会话，活跃会话数: {} (连接ID: {})",
                sessionConnections.size(), clientConnectionId);

        // 安全地关闭所有会话
        int closedSessions = 0;
        for (Map.Entry<Integer, String> entry : sessionConnections.entrySet()) {
            int sessionId = entry.getKey();
            String connectionId = entry.getValue();

            try {
                // 通过ProxyProcessor关闭连接，让outbound处理器负责连接管理
                proxyProcessor.closeConnection(connectionId);
                closedSessions++;

                // 性能指标统计
                PerformanceMetrics.getInstance().decrementActiveSessions();
            } catch (Exception e) {
                logger.warn("关闭连接时发生异常: sessionId={}, connectionId={} (连接ID: {}): {}",
                        sessionId, connectionId, clientConnectionId, e.getMessage());
            }
        }

        // 清理所有映射
        sessionConnections.clear();
        sessionHostKeys.clear();
        reusableSessionIds.clear();
        terminatedSessions.clear();

        if (closedSessions > 0) {
            logger.info("客户端连接 {} 断开，已清理 {} 个后端会话", clientConnectionId, closedSessions);
        }

        // 安全释放缓冲区
        if (buffer != null) {
            try {
                buffer.release();
                logger.debug("已释放客户端连接 {} 的缓冲区", clientConnectionId);
            } catch (Exception e) {
                logger.warn("释放缓冲区时出现异常 (连接ID: {}): {}", clientConnectionId, e.getMessage());
            } finally {
                buffer = null;
            }
        }

        logger.debug("多路复用会话清理完成 (连接ID: {})", clientConnectionId);
    }

    /**
     * 判断是否为连接重置错误
     */
    private boolean isConnectionResetError(Throwable cause) {
        if (cause instanceof java.io.IOException) {
            String message = cause.getMessage();
            return message != null && (message.contains("Connection reset by peer") ||
                    message.contains("Connection reset") ||
                    message.contains("远程主机强迫关闭了一个现有的连接"));
        }
        return false;
    }

    /**
     * 判断是否为通道已关闭错误
     */
    private boolean isClosedChannelError(Throwable cause) {
        // 检查StacklessClosedChannelException和其他通道关闭异常
        if (cause.getClass().getSimpleName().equals("StacklessClosedChannelException")) {
            return true;
        }
        if (cause instanceof java.nio.channels.ClosedChannelException) {
            return true;
        }
        if (cause instanceof java.io.IOException) {
            String message = cause.getMessage();
            return message != null && (message.contains("Channel is closed") ||
                    message.contains("通道已关闭") ||
                    message.contains("Connection is closed"));
        }
        return false;
    }
}
