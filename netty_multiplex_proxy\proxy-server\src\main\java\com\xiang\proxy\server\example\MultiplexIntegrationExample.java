package com.xiang.proxy.server.example;

import com.xiang.proxy.server.config.ProxyProcessorConfig;
import com.xiang.proxy.server.core.EnhancedProxyProcessor;
import com.xiang.proxy.server.factory.EnhancedProxyProcessorFactory;
import com.xiang.proxy.server.inbound.impl.multiplex.MultiplexInboundHandler;
import com.xiang.proxy.server.outbound.impl.TcpDirectOutboundHandler;
import com.xiang.proxy.server.outbound.OutboundConfig;
import com.xiang.proxy.server.router.DefaultRouter;
import com.xiang.proxy.server.router.RouteRule;
import com.xiang.proxy.server.router.RouteMatcher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 多路复用集成示例
 * 展示如何使用EnhancedProxyProcessor处理多路复用请求
 */
public class MultiplexIntegrationExample {
    private static final Logger logger = LoggerFactory.getLogger(MultiplexIntegrationExample.class);
    
    public static void main(String[] args) {
        MultiplexIntegrationExample example = new MultiplexIntegrationExample();
        
        try {
            example.runIntegrationExample();
        } catch (Exception e) {
            logger.error("运行多路复用集成示例时发生异常", e);
        }
    }
    
    public void runIntegrationExample() throws InterruptedException {
        logger.info("启动多路复用集成示例...");
        
        // 1. 创建增强代理处理器
        EnhancedProxyProcessor processor = createEnhancedProcessor();
        
        // 2. 注册出站处理器
        registerOutboundHandlers(processor);
        
        // 3. 注册入站处理器（包括多路复用处理器）
        registerInboundHandlers(processor);
        
        try {
            // 4. 启动处理器
            processor.start();
            logger.info("增强代理处理器已启动");
            
            // 5. 启动监控
            startMonitoring(processor);
            
            // 6. 运行一段时间观察效果
            logger.info("系统运行中，观察连接复用效果...");
            Thread.sleep(60000); // 运行1分钟
            
        } finally {
            // 7. 关闭处理器
            processor.shutdown();
            logger.info("多路复用集成示例结束");
        }
    }
    
    /**
     * 创建增强代理处理器
     */
    private EnhancedProxyProcessor createEnhancedProcessor() {
        // 创建路由器
        DefaultRouter router = createRouter();
        
        // 创建配置 - 针对多路复用优化
        ProxyProcessorConfig config = createMultiplexOptimizedConfig();
        
        // 使用工厂创建处理器
        EnhancedProxyProcessor processor = EnhancedProxyProcessorFactory.createEnhanced(router, config);
        
        logger.info("增强代理处理器创建完成");
        return processor;
    }
    
    /**
     * 创建路由器
     */
    private DefaultRouter createRouter() {
        DefaultRouter router = new DefaultRouter();
        
        // 多路复用TCP路由
        RouteRule multiplexTcpRule = new RouteRule("multiplex-tcp", "多路复用TCP路由", 10, "tcp-direct");
        multiplexTcpRule.addMatcher(new RouteMatcher(RouteMatcher.Type.PROTOCOL, RouteMatcher.Operator.EQUALS, "MULTIPLEX_TCP"));
        router.addRoute(multiplexTcpRule);
        
        // 多路复用UDP路由
        RouteRule multiplexUdpRule = new RouteRule("multiplex-udp", "多路复用UDP路由", 20, "udp-direct");
        multiplexUdpRule.addMatcher(new RouteMatcher(RouteMatcher.Type.PROTOCOL, RouteMatcher.Operator.EQUALS, "MULTIPLEX_UDP"));
        router.addRoute(multiplexUdpRule);
        
        // 通用多路复用路由
        RouteRule multiplexRule = new RouteRule("multiplex", "通用多路复用路由", 30, "tcp-direct");
        multiplexRule.addMatcher(new RouteMatcher(RouteMatcher.Type.PROTOCOL, RouteMatcher.Operator.EQUALS, "MULTIPLEX"));
        router.addRoute(multiplexRule);
        
        // HTTP路由
        RouteRule httpRule = new RouteRule("http", "HTTP路由", 40, "tcp-direct");
        httpRule.addMatcher(new RouteMatcher(RouteMatcher.Type.PROTOCOL, RouteMatcher.Operator.EQUALS, "HTTP"));
        httpRule.addMatcher(new RouteMatcher(RouteMatcher.Type.PORT, RouteMatcher.Operator.EQUALS, "80"));
        router.addRoute(httpRule);
        
        // HTTPS路由
        RouteRule httpsRule = new RouteRule("https", "HTTPS路由", 50, "tcp-direct");
        httpsRule.addMatcher(new RouteMatcher(RouteMatcher.Type.PROTOCOL, RouteMatcher.Operator.EQUALS, "HTTPS"));
        httpsRule.addMatcher(new RouteMatcher(RouteMatcher.Type.PORT, RouteMatcher.Operator.EQUALS, "443"));
        router.addRoute(httpsRule);
        
        // 默认路由
        RouteRule defaultRule = new RouteRule("default", "默认路由", 999, "tcp-direct");
        router.addRoute(defaultRule);
        
        logger.info("路由器配置完成，共 {} 条规则", router.getRouteRules().size());
        return router;
    }
    
    /**
     * 创建多路复用优化配置
     */
    private ProxyProcessorConfig createMultiplexOptimizedConfig() {
        ProxyProcessorConfig config = new ProxyProcessorConfig();
        
        // 基础配置 - 针对多路复用优化
        int cpuCount = Runtime.getRuntime().availableProcessors();
        config.setQueueCount(Math.max(4, cpuCount * 2)); // 更多队列处理并发会话
        config.setQueueCapacity(2000); // 更大的队列容量
        config.setWorkerThreadPrefix("multiplex-enhanced-worker-");
        
        // 批处理配置 - 适合多路复用的小数据包
        config.setBatchSize(5); // 较小的批处理大小
        config.setBatchTimeoutMs(10); // 较短的超时时间，减少延迟
        
        // 启用自适应调整
        config.setEnableAdaptiveAdjustment(true);
        
        // 关闭超时配置
        config.setShutdownTimeoutSeconds(30);
        
        logger.info("多路复用优化配置创建完成: {}", config);
        return config;
    }
    
    /**
     * 注册出站处理器
     */
    private void registerOutboundHandlers(EnhancedProxyProcessor processor) {
        // TCP直连出站处理器
        OutboundConfig tcpConfig = OutboundConfig.defaultConfig();
        tcpConfig.setProperty("connectTimeoutMs", 5000);
        tcpConfig.setProperty("readTimeoutMs", 30000);
        tcpConfig.setProperty("writeTimeoutMs", 30000);
        
        TcpDirectOutboundHandler tcpOutbound = new TcpDirectOutboundHandler("tcp-direct", tcpConfig);
        processor.registerOutboundHandler(tcpOutbound);
        
        // UDP直连出站处理器（如果需要）
        // UdpDirectOutboundHandler udpOutbound = new UdpDirectOutboundHandler("udp-direct", tcpConfig);
        // processor.registerOutboundHandler(udpOutbound);
        
        logger.info("出站处理器注册完成");
    }
    
    /**
     * 注册入站处理器
     */
    private void registerInboundHandlers(EnhancedProxyProcessor processor) {
        // 多路复用入站处理器
        MultiplexInboundHandler multiplexHandler = new MultiplexInboundHandler(processor);
        processor.registerInboundHandler(multiplexHandler);
        
        logger.info("入站处理器注册完成，包括多路复用处理器");
    }
    
    /**
     * 启动监控
     */
    private void startMonitoring(EnhancedProxyProcessor processor) {
        ScheduledExecutorService monitorExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "multiplex-monitor");
            t.setDaemon(true);
            return t;
        });
        
        // 每15秒输出一次统计信息
        monitorExecutor.scheduleAtFixedRate(() -> {
            try {
                printMultiplexStatistics(processor);
            } catch (Exception e) {
                logger.error("监控任务执行异常", e);
            }
        }, 15, 15, TimeUnit.SECONDS);
        
        logger.info("多路复用监控任务已启动");
    }
    
    /**
     * 打印多路复用统计信息
     */
    private void printMultiplexStatistics(EnhancedProxyProcessor processor) {
        logger.info("=== 多路复用统计信息 ===");
        
        // 队列统计
        try {
            var queueStats = processor.getQueueStats();
            logger.info("队列统计: 队列数={}, 总队列大小={}, 已处理请求={}", 
                queueStats.getQueueCount(), queueStats.getTotalQueueSize(), queueStats.getProcessedRequests());
                
            // 计算队列使用率
            double queueUsage = (double) queueStats.getTotalQueueSize() / 
                (queueStats.getQueueCount() * processor.getConfig().getQueueCapacity()) * 100;
            logger.info("队列使用率: {:.2f}%", queueUsage);
            
        } catch (Exception e) {
            logger.warn("获取队列统计失败", e);
        }
        
        // 连接管理器统计
        try {
            var connectionStats = processor.getConnectionStats();
            int totalConnections = 0;
            int activeConnections = 0;
            long totalMessages = 0;
            long queuedMessages = 0;
            
            for (var entry : connectionStats.entrySet()) {
                var stats = entry.getValue();
                totalConnections += stats.getTotalConnections();
                activeConnections += stats.getActiveConnections();
                totalMessages += stats.getTotalMessages();
                queuedMessages += stats.getQueuedMessages();
            }
            
            logger.info("连接复用统计: 总连接={}, 活跃连接={}, 总消息={}, 队列消息={}", 
                totalConnections, activeConnections, totalMessages, queuedMessages);
                
            // 计算连接复用率
            if (activeConnections > 0) {
                double reuseRate = (double) totalConnections / activeConnections;
                logger.info("连接复用率: {:.2f}", reuseRate);
            }
            
        } catch (Exception e) {
            logger.warn("获取连接统计失败", e);
        }
        
        // 性能指标
        try {
            var metrics = processor.getMetrics();
            if (metrics != null) {
                logger.info("性能指标: 成功率={:.2f}%, 平均处理时间={}ms", 
                    metrics.getSuccessRate() * 100, metrics.getAverageProcessingTime());
            }
        } catch (Exception e) {
            logger.warn("获取性能指标失败", e);
        }
        
        // 自适应管理器建议
        try {
            if (processor.getAdaptiveManager() != null) {
                String recommendations = processor.getAdaptiveManager().getAdjustmentRecommendations();
                if (recommendations != null && !recommendations.trim().isEmpty()) {
                    logger.info("自适应建议:\n{}", recommendations);
                }
            }
        } catch (Exception e) {
            logger.warn("获取自适应建议失败", e);
        }
        
        logger.info("=== 统计信息结束 ===");
    }
}
